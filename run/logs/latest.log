[297月2025 19:43:46.059] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--launchTarget, forgeclientuserdev, --version, MOD_DEV, --assetIndex, 5, --assetsDir, C:\Users\<USER>\.gradle\caches\forge_gradle\assets, --gameDir, ., --fml.forgeVersion, 47.4.4, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, ********.114412, --mixin.config, kyokuerabu.mixins.json]
[297月2025 19:43:46.067] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 17.0.12 by Oracle Corporation; OS Windows 11 arch amd64 version 10.0
[297月2025 19:43:46.216] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: Loading ImmediateWindowProvider fmlearlywindow
[297月2025 19:43:46.304] [main/INFO] [EARLYDISPLAY/]: Trying GL version 4.6
[297月2025 19:43:46.532] [main/INFO] [EARLYDISPLAY/]: Requested GL version 4.6 got version 4.6
[297月2025 19:43:46.651] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.spongepowered/mixin/0.8.5/9d1c0c3a304ae6697ecd477218fa61b850bf57fc/mixin-0.8.5.jar%23128!/ Service=ModLauncher Env=CLIENT
[297月2025 19:43:46.716] [pool-2-thread-1/INFO] [EARLYDISPLAY/]: GL info: NVIDIA GeForce GTX 1080/PCIe/SSE2 GL version 4.6.0 NVIDIA 560.94, NVIDIA Corporation
[297月2025 19:43:47.092] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\javafmllanguage\1.20.1-47.4.4\c05c04093d241dea1d0f54782d19cfe5db9d0aac\javafmllanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:43:47.099] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\lowcodelanguage\1.20.1-47.4.4\8a8bd6d44f3f0e6a1145b9066eac84fdcfbe7108\lowcodelanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:43:47.102] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mclanguage\1.20.1-47.4.4\a8876f0a1a04237fc48a4a73692ea08e0443d2ba\mclanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:43:47.106] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlcore\1.20.1-47.4.4\55a693260a8a7b851c3531972ef997294c191b3e\fmlcore-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:43:47.319] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: No dependencies to load found. Skipping!
[297月2025 19:43:49.447] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'forgeclientuserdev' with arguments [--version, MOD_DEV, --gameDir, ., --assetsDir, C:\Users\<USER>\.gradle\caches\forge_gradle\assets, --assetIndex, 5]
[297月2025 19:43:49.581] [main/WARN] [mixin/]: Reference map 'kyokuerabu.refmap.json' for kyokuerabu.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 19:43:55.214] [Datafixer Bootstrap/INFO] [com.mojang.datafixers.DataFixerBuilder/]: 188 Datafixer optimizations took 182 milliseconds
[297月2025 19:43:57.381] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/.gradle/caches/forge_gradle/minecraft_user_repo/net/minecraftforge/forge/1.20.1-47.4.4_mapped_official_1.20.1/forge-1.20.1-47.4.4_mapped_official_1.20.1.jar%23191!/assets/.mcassetsroot' uses unexpected schema
[297月2025 19:43:57.382] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/.gradle/caches/forge_gradle/minecraft_user_repo/net/minecraftforge/forge/1.20.1-47.4.4_mapped_official_1.20.1/forge-1.20.1-47.4.4_mapped_official_1.20.1.jar%23191!/data/.mcassetsroot' uses unexpected schema
[297月2025 19:43:57.429] [Render thread/INFO] [com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService/]: Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[297月2025 19:43:57.448] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Setting user: Dev
[297月2025 19:43:57.605] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Backend library: LWJGL version 3.3.1 build 7
[297月2025 19:43:57.935] [modloading-worker-0/INFO] [net.minecraftforge.common.ForgeMod/FORGEMOD]: Forge mod loading, version 47.4.4, for MC 1.20.1 with MCP ********.114412
[297月2025 19:43:57.936] [modloading-worker-0/INFO] [net.minecraftforge.common.MinecraftForge/FORGE]: MinecraftForge v47.4.4 Initialized
[297月2025 19:43:58.498] [Render thread/INFO] [net.minecraft.server.packs.repository.FolderRepositorySource/]: Found non-pack entry '.\resourcepacks\kyokuerabu_music', ignoring
[297月2025 19:43:58.942] [Render thread/INFO] [net.minecraftforge.gametest.ForgeGameTestHooks/]: Enabled Gametest Namespaces: [kyokuerabu]
[297月2025 19:43:59.128] [Render thread/INFO] [net.minecraft.server.packs.resources.ReloadableResourceManager/]: Reloading ResourceManager: vanilla, mod_resources
[297月2025 19:43:59.377] [Worker-Main-1/INFO] [net.minecraft.client.gui.font.providers.UnihexProvider/]: Found unifont_all_no_pua-15.0.06.hex, loading
[297月2025 19:43:59.385] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Starting version check at https://files.minecraftforge.net/net/minecraftforge/forge/promotions_slim.json
[297月2025 19:44:01.078] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Found status: AHEAD Current: 47.4.4 Target: null
[297月2025 19:44:02.457] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:item.goat_horn.play
[297月2025 19:44:02.458] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:entity.goat.screaming.horn_break
[297月2025 19:44:02.520] [Render thread/INFO] [com.mojang.blaze3d.audio.Library/]: OpenAL initialized on device OpenAL Soft on 扬声器 (EDIFIER N300)
[297月2025 19:44:02.522] [Render thread/INFO] [net.minecraft.client.sounds.SoundEngine/SOUNDS]: Sound engine started
[297月2025 19:44:02.678] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[297月2025 19:44:02.686] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[297月2025 19:44:02.687] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[297月2025 19:44:02.688] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[297月2025 19:44:02.688] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[297月2025 19:44:02.691] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[297月2025 19:44:02.694] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[297月2025 19:44:02.694] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[297月2025 19:44:02.694] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[297月2025 19:44:03.025] [Render thread/WARN] [net.minecraft.client.renderer.ShaderInstance/]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[297月2025 19:44:03.100] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[297月2025 19:44:03.104] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[297月2025 19:44:03.105] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[297月2025 19:44:03.614] [Realms Notification Availability checker #1/INFO] [com.mojang.realmsclient.client.RealmsClient/]: Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: 0
[297月2025 19:44:07.154] [Render thread/INFO] [net.minecraftforge.registries.GameData/REGISTRIES]: Injecting existing registry data into this CLIENT instance
[297月2025 19:44:09.267] [Render thread/INFO] [net.minecraft.world.item.crafting.RecipeManager/]: Loaded 7 recipes
[297月2025 19:44:09.433] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 1271 advancements
[297月2025 19:44:09.871] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Starting integrated minecraft server version 1.20.1
[297月2025 19:44:09.871] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Generating keypair
[297月2025 19:44:10.886] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Preparing start region for dimension minecraft:overworld
[297月2025 19:44:13.489] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:44:13.489] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:44:13.490] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:44:13.490] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:44:13.490] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:44:13.490] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:44:13.903] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：28%
[297月2025 19:44:14.283] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: Time elapsed: 3394 ms
[297月2025 19:44:14.283] [Server thread/INFO] [net.minecraftforge.server.permission.PermissionAPI/]: Successfully initialized permission handler forge:default_handler
[297月2025 19:44:14.283] [Server thread/INFO] [top.lacrus.kyokuerabu.Kyokuerabu/]: Local music directory already exists: D:\Projects\Kyokuerabu\run\music
[297月2025 19:44:14.283] [Server thread/INFO] [top.lacrus.kyokuerabu.Kyokuerabu/]: Kyokuerabu server started - using packet-based music transfer
[297月2025 19:44:14.411] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing view distance to 12, from 10
[297月2025 19:44:14.414] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing simulation distance to 12, from 0
[297月2025 19:44:14.608] [Render thread/WARN] [io.netty.util.internal.SystemPropertyUtil/]: Unable to parse the boolean system property 'java.net.preferIPv6Addresses':system - using the default value: false
[297月2025 19:44:15.610] [Netty Local Client IO #0/INFO] [net.minecraftforge.network.NetworkHooks/]: Connected to a modded server.
[297月2025 19:44:15.705] [Server thread/INFO] [net.minecraft.server.players.PlayerList/]: Dev[local:E:dacd56c5] logged in with entity id 174 at (-86.9231788167608, 68.79673560066871, -146.3505282479096)
[297月2025 19:44:15.786] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Dev加入了游戏
[297月2025 19:44:16.671] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 2 advancements
[297月2025 19:44:24.247] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: [Dev: 已向 1 个玩家发送音乐播放请求: fssx.mp3]
[297月2025 19:44:24.248] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: [Dev: 已向玩家 Dev 发送本地音乐播放指令: fssx.mp3]
[297月2025 19:44:24.254] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: MusicCacheManager.handleFileInfo called
[297月2025 19:44:24.254] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File: fssx.mp3
[297月2025 19:44:24.254] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Server hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:44:24.254] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Server file size: 10359162
[297月2025 19:44:24.256] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Local file exists, checking hash...
[297月2025 19:44:24.256] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Local file size: 10359162
[297月2025 19:44:24.256] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Calculating hash for: client_music\fssx.mp3
[297月2025 19:44:24.258] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] 已向 1 个玩家发送音乐播放请求: fssx.mp3
[297月2025 19:44:24.266] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] 已向玩家 Dev 发送本地音乐播放指令: fssx.mp3
[297月2025 19:44:24.268] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Calculated hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:44:24.268] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Local hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Server hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Hash matches, playing local file directly
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: ========== Playing from client_music ==========
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File: D:\Projects\Kyokuerabu\run\client_music\fssx.mp3
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File exists: true
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File size: 10359162 bytes
[297月2025 19:44:24.269] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Calling MinecraftJLayerPlayer.playMusicFile...
[297月2025 19:44:24.281] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: MinecraftJLayerPlayer.playMusicFile call completed
[297月2025 19:44:24.281] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Sending cache response: needDownload=false, reason=哈希匹配，直接播放本地文件
[297月2025 19:44:24.281] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Sent cache response to server
[297月2025 19:44:24.283] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] [Kyokuerabu] ♪ 开始播放: fssx
[297月2025 19:44:24.284] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] [Kyokuerabu] 哈希匹配，直接播放本地文件
[297月2025 19:44:31.584] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[297月2025 19:44:31.592] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[297月2025 19:44:31.610] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[297月2025 19:44:31.613] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
