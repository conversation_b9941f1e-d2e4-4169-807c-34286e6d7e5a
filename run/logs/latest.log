[297月2025 19:32:03.178] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher running: args [--launchTarget, forgeclientuserdev, --version, MOD_DEV, --assetIndex, 5, --assetsDir, C:\Users\<USER>\.gradle\caches\forge_gradle\assets, --gameDir, ., --fml.forgeVersion, 47.4.4, --fml.mcVersion, 1.20.1, --fml.forgeGroup, net.minecraftforge, --fml.mcpVersion, ********.114412, --mixin.config, kyokuerabu.mixins.json]
[297月2025 19:32:03.183] [main/INFO] [cpw.mods.modlauncher.Launcher/MODLAUNCHER]: ModLauncher 10.0.9+10.0.9+main.dcd20f30 starting: java version 17.0.12 by Oracle Corporation; OS Windows 11 arch amd64 version 10.0
[297月2025 19:32:03.346] [main/INFO] [net.minecraftforge.fml.loading.ImmediateWindowHandler/]: Loading ImmediateWindowProvider fmlearlywindow
[297月2025 19:32:03.446] [main/INFO] [EARLYDISPLAY/]: Trying GL version 4.6
[297月2025 19:32:03.661] [main/INFO] [EARLYDISPLAY/]: Requested GL version 4.6 got version 4.6
[297月2025 19:32:03.754] [main/INFO] [mixin/]: SpongePowered MIXIN Subsystem Version=0.8.5 Source=union:/C:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.spongepowered/mixin/0.8.5/9d1c0c3a304ae6697ecd477218fa61b850bf57fc/mixin-0.8.5.jar%23128!/ Service=ModLauncher Env=CLIENT
[297月2025 19:32:03.855] [pool-2-thread-1/INFO] [EARLYDISPLAY/]: GL info: NVIDIA GeForce GTX 1080/PCIe/SSE2 GL version 4.6.0 NVIDIA 560.94, NVIDIA Corporation
[297月2025 19:32:04.309] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\javafmllanguage\1.20.1-47.4.4\c05c04093d241dea1d0f54782d19cfe5db9d0aac\javafmllanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:32:04.313] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\lowcodelanguage\1.20.1-47.4.4\8a8bd6d44f3f0e6a1145b9066eac84fdcfbe7108\lowcodelanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:32:04.317] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mclanguage\1.20.1-47.4.4\a8876f0a1a04237fc48a4a73692ea08e0443d2ba\mclanguage-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:32:04.323] [main/WARN] [net.minecraftforge.fml.loading.moddiscovery.ModFileParser/LOADING]: Mod file C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlcore\1.20.1-47.4.4\55a693260a8a7b851c3531972ef997294c191b3e\fmlcore-1.20.1-47.4.4.jar is missing mods.toml file
[297月2025 19:32:04.436] [main/INFO] [net.minecraftforge.fml.loading.moddiscovery.JarInJarDependencyLocator/]: No dependencies to load found. Skipping!
[297月2025 19:32:06.584] [main/INFO] [cpw.mods.modlauncher.LaunchServiceHandler/MODLAUNCHER]: Launching target 'forgeclientuserdev' with arguments [--version, MOD_DEV, --gameDir, ., --assetsDir, C:\Users\<USER>\.gradle\caches\forge_gradle\assets, --assetIndex, 5]
[297月2025 19:32:06.701] [main/WARN] [mixin/]: Reference map 'kyokuerabu.refmap.json' for kyokuerabu.mixins.json could not be read. If this is a development environment you can ignore this message
[297月2025 19:32:11.745] [Datafixer Bootstrap/INFO] [com.mojang.datafixers.DataFixerBuilder/]: 188 Datafixer optimizations took 184 milliseconds
[297月2025 19:32:14.315] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/.gradle/caches/forge_gradle/minecraft_user_repo/net/minecraftforge/forge/1.20.1-47.4.4_mapped_official_1.20.1/forge-1.20.1-47.4.4_mapped_official_1.20.1.jar%23191!/assets/.mcassetsroot' uses unexpected schema
[297月2025 19:32:14.315] [Render thread/WARN] [net.minecraft.server.packs.VanillaPackResourcesBuilder/]: Assets URL 'union:/C:/Users/<USER>/.gradle/caches/forge_gradle/minecraft_user_repo/net/minecraftforge/forge/1.20.1-47.4.4_mapped_official_1.20.1/forge-1.20.1-47.4.4_mapped_official_1.20.1.jar%23191!/data/.mcassetsroot' uses unexpected schema
[297月2025 19:32:14.365] [Render thread/INFO] [com.mojang.authlib.yggdrasil.YggdrasilAuthenticationService/]: Environment: authHost='https://authserver.mojang.com', accountsHost='https://api.mojang.com', sessionHost='https://sessionserver.mojang.com', servicesHost='https://api.minecraftservices.com', name='PROD'
[297月2025 19:32:14.384] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Setting user: Dev
[297月2025 19:32:14.551] [Render thread/INFO] [net.minecraft.client.Minecraft/]: Backend library: LWJGL version 3.3.1 build 7
[297月2025 19:32:14.959] [modloading-worker-0/INFO] [net.minecraftforge.common.ForgeMod/FORGEMOD]: Forge mod loading, version 47.4.4, for MC 1.20.1 with MCP ********.114412
[297月2025 19:32:14.960] [modloading-worker-0/INFO] [net.minecraftforge.common.MinecraftForge/FORGE]: MinecraftForge v47.4.4 Initialized
[297月2025 19:32:15.618] [Render thread/INFO] [net.minecraft.server.packs.repository.FolderRepositorySource/]: Found non-pack entry '.\resourcepacks\kyokuerabu_music', ignoring
[297月2025 19:32:16.195] [Render thread/INFO] [net.minecraftforge.gametest.ForgeGameTestHooks/]: Enabled Gametest Namespaces: [kyokuerabu]
[297月2025 19:32:16.360] [Render thread/INFO] [net.minecraft.server.packs.resources.ReloadableResourceManager/]: Reloading ResourceManager: vanilla, mod_resources
[297月2025 19:32:16.616] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Starting version check at https://files.minecraftforge.net/net/minecraftforge/forge/promotions_slim.json
[297月2025 19:32:16.656] [Worker-Main-3/INFO] [net.minecraft.client.gui.font.providers.UnihexProvider/]: Found unifont_all_no_pua-15.0.06.hex, loading
[297月2025 19:32:17.992] [Forge Version Check/INFO] [net.minecraftforge.fml.VersionChecker/]: [forge] Found status: AHEAD Current: 47.4.4 Target: null
[297月2025 19:32:19.640] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:item.goat_horn.play
[297月2025 19:32:19.640] [Render thread/WARN] [net.minecraft.client.sounds.SoundEngine/]: Missing sound for event: minecraft:entity.goat.screaming.horn_break
[297月2025 19:32:19.684] [Render thread/INFO] [com.mojang.blaze3d.audio.Library/]: OpenAL initialized on device OpenAL Soft on 扬声器 (EDIFIER N300)
[297月2025 19:32:19.685] [Render thread/INFO] [net.minecraft.client.sounds.SoundEngine/SOUNDS]: Sound engine started
[297月2025 19:32:19.821] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x512x4 minecraft:textures/atlas/blocks.png-atlas
[297月2025 19:32:19.829] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x4 minecraft:textures/atlas/signs.png-atlas
[297月2025 19:32:19.831] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/shield_patterns.png-atlas
[297月2025 19:32:19.832] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x512x4 minecraft:textures/atlas/banner_patterns.png-atlas
[297月2025 19:32:19.833] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 1024x1024x4 minecraft:textures/atlas/armor_trims.png-atlas
[297月2025 19:32:19.835] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x4 minecraft:textures/atlas/chest.png-atlas
[297月2025 19:32:19.837] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x64x4 minecraft:textures/atlas/decorated_pot.png-atlas
[297月2025 19:32:19.837] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/shulker_boxes.png-atlas
[297月2025 19:32:19.838] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 512x256x4 minecraft:textures/atlas/beds.png-atlas
[297月2025 19:32:20.268] [Render thread/WARN] [net.minecraft.client.renderer.ShaderInstance/]: Shader rendertype_entity_translucent_emissive could not find sampler named Sampler2 in the specified shader program.
[297月2025 19:32:20.330] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/particles.png-atlas
[297月2025 19:32:20.332] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 256x256x0 minecraft:textures/atlas/paintings.png-atlas
[297月2025 19:32:20.332] [Render thread/INFO] [net.minecraft.client.renderer.texture.TextureAtlas/]: Created: 128x128x0 minecraft:textures/atlas/mob_effects.png-atlas
[297月2025 19:32:20.739] [Realms Notification Availability checker #1/INFO] [com.mojang.realmsclient.client.RealmsClient/]: Could not authorize you against Realms server: java.lang.RuntimeException: Failed to parse into SignedJWT: 0
[297月2025 19:32:25.954] [Render thread/INFO] [net.minecraftforge.registries.GameData/REGISTRIES]: Injecting existing registry data into this CLIENT instance
[297月2025 19:32:27.949] [Render thread/INFO] [net.minecraft.world.item.crafting.RecipeManager/]: Loaded 7 recipes
[297月2025 19:32:28.148] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 1271 advancements
[297月2025 19:32:28.703] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Starting integrated minecraft server version 1.20.1
[297月2025 19:32:28.703] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Generating keypair
[297月2025 19:32:29.874] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Preparing start region for dimension minecraft:overworld
[297月2025 19:32:32.673] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:32.673] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:32.674] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:32.674] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:32.674] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:32.674] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:33.155] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：0%
[297月2025 19:32:33.395] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: 准备生成区域中：68%
[297月2025 19:32:33.494] [Server thread/INFO] [net.minecraftforge.server.permission.PermissionAPI/]: Successfully initialized permission handler forge:default_handler
[297月2025 19:32:33.494] [Server thread/INFO] [top.lacrus.kyokuerabu.Kyokuerabu/]: Local music directory already exists: D:\Projects\Kyokuerabu\run\music
[297月2025 19:32:33.494] [Server thread/INFO] [top.lacrus.kyokuerabu.Kyokuerabu/]: Kyokuerabu server started - using packet-based music transfer
[297月2025 19:32:33.502] [Render thread/INFO] [net.minecraft.server.level.progress.LoggerChunkProgressListener/]: Time elapsed: 3624 ms
[297月2025 19:32:33.622] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing view distance to 12, from 10
[297月2025 19:32:33.624] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Changing simulation distance to 12, from 0
[297月2025 19:32:33.751] [Render thread/WARN] [io.netty.util.internal.SystemPropertyUtil/]: Unable to parse the boolean system property 'java.net.preferIPv6Addresses':system - using the default value: false
[297月2025 19:32:34.591] [Netty Local Client IO #0/INFO] [net.minecraftforge.network.NetworkHooks/]: Connected to a modded server.
[297月2025 19:32:34.674] [Server thread/INFO] [net.minecraft.server.players.PlayerList/]: Dev[local:E:fc240ec2] logged in with entity id 172 at (-83.0501333347461, 68.0, -138.18048824921044)
[297月2025 19:32:34.771] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Dev加入了游戏
[297月2025 19:32:35.688] [Render thread/INFO] [net.minecraft.advancements.AdvancementList/]: Loaded 2 advancements
[297月2025 19:32:35.935] [Server thread/WARN] [net.minecraft.world.level.entity.PersistentEntitySectionManager/]: UUID of added entity already exists: Sheep['绵羊'/349, l='ServerLevel[新的世界]', x=-319.10, y=73.00, z=-168.00]
[297月2025 19:32:37.106] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[297月2025 19:32:37.334] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[297月2025 19:32:37.933] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[297月2025 19:32:37.934] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
[297月2025 19:33:03.959] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: [Dev: 已向 1 个玩家发送音乐播放请求: fssx.mp3]
[297月2025 19:33:03.960] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: [Dev: 已向玩家 Dev 发送本地音乐播放指令: fssx.mp3]
[297月2025 19:33:03.962] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: MusicCacheManager.handleFileInfo called
[297月2025 19:33:03.962] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File: fssx.mp3
[297月2025 19:33:03.962] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Server hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:33:03.962] [Render thread/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Server file size: 10359162
[297月2025 19:33:03.964] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Local file exists, checking hash...
[297月2025 19:33:03.964] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Local file size: 10359162
[297月2025 19:33:03.964] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Calculating hash for: client_music\fssx.mp3
[297月2025 19:33:03.966] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] 已向 1 个玩家发送音乐播放请求: fssx.mp3
[297月2025 19:33:03.972] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] 已向玩家 Dev 发送本地音乐播放指令: fssx.mp3
[297月2025 19:33:03.979] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Calculated hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:33:03.979] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Local hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:33:03.979] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Server hash: 3a3115b9cb4626ae7d5dde66b0c38a9ffc1ef4583453f2f698fa15bd9e09e01d
[297月2025 19:33:03.979] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Hash matches, playing local file directly
[297月2025 19:33:03.979] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: ========== Playing from client_music ==========
[297月2025 19:33:03.979] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File: D:\Projects\Kyokuerabu\run\client_music\fssx.mp3
[297月2025 19:33:03.980] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File exists: true
[297月2025 19:33:03.980] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: File size: 10359162 bytes
[297月2025 19:33:03.980] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Calling MinecraftJLayerPlayer.playMusicFile...
[297月2025 19:33:03.992] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Starting async duration calculation for: client_music\fssx.mp3
[297月2025 19:33:03.993] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: MinecraftJLayerPlayer.playMusicFile call completed
[297月2025 19:33:03.993] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Sending cache response: needDownload=false, reason=哈希匹配，直接播放本地文件
[297月2025 19:33:03.993] [ForkJoinPool.commonPool-worker-2/INFO] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Getting duration for file: fssx.mp3 (client_music\fssx.mp3)
[297月2025 19:33:03.994] [ForkJoinPool.commonPool-worker-1/INFO] [top.lacrus.kyokuerabu.client.MusicCacheManager/]: Sent cache response to server
[297月2025 19:33:03.994] [ForkJoinPool.commonPool-worker-2/ERROR] [top.lacrus.kyokuerabu.client.MusicDurationReader/]: Failed to get duration async: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
java.util.concurrent.CompletionException: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:315) ~[?:?]
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:320) ~[?:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1770) ~[?:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.exec(CompletableFuture.java:1760) ~[?:?]
	at java.util.concurrent.ForkJoinTask.doExec(ForkJoinTask.java:373) ~[?:?]
	at java.util.concurrent.ForkJoinPool$WorkQueue.topLevelExec(ForkJoinPool.java:1182) ~[?:?]
	at java.util.concurrent.ForkJoinPool.scan(ForkJoinPool.java:1655) ~[?:?]
	at java.util.concurrent.ForkJoinPool.runWorker(ForkJoinPool.java:1622) ~[?:?]
	at java.util.concurrent.ForkJoinWorkerThread.run(ForkJoinWorkerThread.java:165) ~[?:?]
Caused by: java.lang.NoClassDefFoundError: javazoom/jl/decoder/Bitstream
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getMp3Duration(MusicDurationReader.java:99) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getDurationMillis(MusicDurationReader.java:41) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.lambda$getDurationAsync$0(MusicDurationReader.java:249) ~[main/:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[?:?]
	... 6 more
Caused by: java.lang.ClassNotFoundException: javazoom.jl.decoder.Bitstream
	at jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[?:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:137) ~[securejarhandler-2.1.10.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at cpw.mods.cl.ModuleClassLoader.loadClass(ModuleClassLoader.java:137) ~[securejarhandler-2.1.10.jar:?]
	at java.lang.ClassLoader.loadClass(ClassLoader.java:525) ~[?:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getMp3Duration(MusicDurationReader.java:99) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.getDurationMillis(MusicDurationReader.java:41) ~[main/:?]
	at top.lacrus.kyokuerabu.client.MusicDurationReader.lambda$getDurationAsync$0(MusicDurationReader.java:249) ~[main/:?]
	at java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1768) ~[?:?]
	... 6 more
[297月2025 19:33:03.996] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] [Kyokuerabu] ♪ 开始播放: fssx
[297月2025 19:33:04.019] [Render thread/INFO] [net.minecraft.client.gui.components.ChatComponent/]: [System] [CHAT] [Kyokuerabu] 哈希匹配，直接播放本地文件
[297月2025 19:33:09.995] [Server thread/INFO] [net.minecraft.client.server.IntegratedServer/]: Saving and pausing game...
[297月2025 19:33:09.999] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:overworld
[297月2025 19:33:10.010] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_nether
[297月2025 19:33:10.011] [Server thread/INFO] [net.minecraft.server.MinecraftServer/]: Saving chunks for level 'ServerLevel[新的世界]'/minecraft:the_end
