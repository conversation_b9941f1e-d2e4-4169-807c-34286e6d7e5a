package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.slf4j.Logger;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@OnlyIn(Dist.CLIENT)
public class MinecraftJLayerPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Process currentPlayerProcess;
    private static Thread currentBuiltinPlayerThread;
    private static Object currentBuiltinPlayer; // 使用Object类型避免编译时依赖
    private static volatile boolean shouldStopBuiltin = false;

    public static void playMusicFile(Path musicFile, String fileName) {
        try {
            if (!Files.exists(musicFile)) {
                sendMessage("§c[<PERSON>yokuerabu] 文件不存在");
                return;
            }

            // 停止当前播放
            stopCurrentMusic();

            // 启动外部播放器
            startExternalPlayer(musicFile, fileName);

        } catch (Exception e) {
            LOGGER.error("Error in playMusicFile: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 播放失败: " + e.getMessage());
        }
    }

    public static void playMusicFromMemory(String fileName, byte[] musicData) {
        try {
            // 停止当前播放
            stopCurrentMusic();

            // 将内存数据保存为临时文件，然后播放
            Path tempFile = saveMemoryDataToTempFile(fileName, musicData);
            startExternalPlayer(tempFile, fileName);

        } catch (Exception e) {
            LOGGER.error("Error in playMusicFromMemory: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 内存播放失败: " + e.getMessage());
        }
    }

    private static void startExternalPlayer(Path musicFile, String fileName) {
        try {
            // 获取当前Java可执行文件路径
            String javaHome = System.getProperty("java.home");
            String javaBin = javaHome + File.separator + "bin" + File.separator + "java.exe";

            // 如果是Linux/Mac，使用java而不是java.exe
            if (!Files.exists(Paths.get(javaBin))) {
                javaBin = javaHome + File.separator + "bin" + File.separator + "java";
            }

            // 获取当前classpath，并确保包含jlayer依赖
            String classpath = System.getProperty("java.class.path");

            // 尝试多个可能的jlayer jar位置
            String[] possibleJarPaths = {
                "libs" + File.separator + "jl1.0.1.jar",
                ".." + File.separator + "libs" + File.separator + "jl1.0.1.jar",
                "mods" + File.separator + "libs" + File.separator + "jl1.0.1.jar",
                System.getProperty("user.dir") + File.separator + "libs" + File.separator + "jl1.0.1.jar"
            };

            boolean jarFound = false;
            for (String jarPath : possibleJarPaths) {
                if (Files.exists(Paths.get(jarPath))) {
                    classpath = classpath + File.pathSeparator + Paths.get(jarPath).toAbsolutePath().toString();
                    LOGGER.info("Added jl1.0.1.jar to classpath from: {}", jarPath);
                    jarFound = true;
                    break;
                }
            }

            if (!jarFound) {
                LOGGER.warn("Could not find jl1.0.1.jar in any expected location");
                // 记录当前工作目录和classpath以便调试
                LOGGER.info("Current working directory: {}", System.getProperty("user.dir"));
                LOGGER.info("Current classpath: {}", classpath);
            }

            // 获取当前进程ID
            String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];

            // 构建命令，传递当前进程ID、文件路径和音量
            ProcessBuilder pb = new ProcessBuilder(
                javaBin,
                "-cp", classpath,
                "top.lacrus.kyokuerabu.standalone.StandaloneAudioPlayer",
                musicFile.toAbsolutePath().toString(),
                currentPid,
                String.valueOf(MusicConfig.getVolume())
            );

            pb.redirectErrorStream(true);
            // 临时启用输出以便调试
            pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);

            LOGGER.info("Starting external player with command: {}", pb.command());
            LOGGER.info("Working directory: {}", System.getProperty("user.dir"));
            LOGGER.info("Final classpath: {}", classpath);

            currentPlayerProcess = pb.start();

            // 等待一小段时间检查进程是否立即退出
            Thread.sleep(500);
            if (!currentPlayerProcess.isAlive()) {
                int exitCode = currentPlayerProcess.exitValue();
                LOGGER.error("External player process exited immediately with code: {}", exitCode);
                sendMessage("§c[Kyokuerabu] 外部播放器启动失败，尝试使用内置播放器");

                // 尝试使用内置播放器作为备用方案
                try {
                    playWithBuiltinPlayer(musicFile, fileName);
                    return;
                } catch (Exception e) {
                    LOGGER.error("Built-in player also failed: {}", e.getMessage());
                    sendMessage("§c[Kyokuerabu] 内置播放器也失败了: " + e.getMessage());
                    return;
                }
            }

            // 更新HUD显示
            String displayName = fileName.replaceAll("\\.[^.]*$", ""); // 移除扩展名
            System.out.println("[Kyokuerabu] MinecraftJLayerPlayer: Updating HUD for " + displayName);
            System.out.println("[Kyokuerabu] Music file path: " + musicFile);
            System.out.println("[Kyokuerabu] Show HUD enabled: " + MusicConfig.showHud());

            if (MusicConfig.showHud()) {
                System.out.println("[Kyokuerabu] Updating HUD");
                MusicHUD.setCurrentMusic(displayName, getCurrentPlayerName());
            } else {
                System.out.println("[Kyokuerabu] HUD disabled, skipping HUD update");
            }

            sendMessage("§a[Kyokuerabu] ♪ 开始播放: " + displayName);

        } catch (Exception e) {
            LOGGER.error("Failed to start external player: " + e.getMessage(), e);
            sendMessage("§c[Kyokuerabu] 启动播放器失败: " + e.getMessage());
        }
    }

    private static Path saveMemoryDataToTempFile(String fileName, byte[] musicData) throws IOException {
        Path tempDir = Files.createTempDirectory("kyokuerabu_music");
        Path tempFile = tempDir.resolve(fileName);
        Files.write(tempFile, musicData);
        return tempFile;
    }

    private static void playWithBuiltinPlayer(Path musicFile, String fileName) throws Exception {
        LOGGER.info("Attempting to play with built-in player: {}", musicFile);

        // 停止当前的内置播放器
        stopBuiltinPlayer();

        // 检查jlayer是否可用
        try {
            Class.forName("javazoom.jl.player.advanced.AdvancedPlayer");
        } catch (ClassNotFoundException e) {
            throw new Exception("JLayer library not available for built-in player");
        }

        shouldStopBuiltin = false;

        // 在后台线程中播放音乐
        currentBuiltinPlayerThread = new Thread(() -> {
            try {
                FileInputStream fis = new FileInputStream(musicFile.toFile());
                BufferedInputStream bis = new BufferedInputStream(fis);

                // 使用反射创建AdvancedPlayer以避免编译时依赖
                Class<?> playerClass = Class.forName("javazoom.jl.player.advanced.AdvancedPlayer");
                currentBuiltinPlayer = playerClass.getConstructor(java.io.InputStream.class).newInstance(bis);

                LOGGER.info("Built-in player created successfully for: {}", fileName);

                // 调用play方法
                LOGGER.info("Starting playback with built-in player: {}", fileName);

                // 检查是否应该停止
                if (shouldStopBuiltin) {
                    LOGGER.info("Built-in player stop requested before play");
                    return;
                }

                playerClass.getMethod("play").invoke(currentBuiltinPlayer);

                LOGGER.info("Built-in player finished playing: {}", fileName);

                // 播放完成后清除HUD
                if (!shouldStopBuiltin) {
                    try {
                        Minecraft.getInstance().execute(() -> {
                            MusicHUD.clearCurrentMusic();
                        });
                    } catch (Exception e) {
                        LOGGER.warn("Failed to clear HUD: {}", e.getMessage());
                    }
                }

            } catch (Exception e) {
                if (!shouldStopBuiltin) {
                    LOGGER.error("Built-in player error: {}", e.getMessage());
                    // 发送错误消息到主线程
                    try {
                        Minecraft.getInstance().execute(() -> {
                            sendMessage("§c[Kyokuerabu] 内置播放器错误: " + e.getMessage());
                        });
                    } catch (Exception msgError) {
                        // 忽略消息发送错误
                    }
                }
            } finally {
                // 清理播放器引用
                synchronized (MinecraftJLayerPlayer.class) {
                    if (currentBuiltinPlayer != null) {
                        currentBuiltinPlayer = null;
                    }
                }
            }
        });

        currentBuiltinPlayerThread.setDaemon(true);
        currentBuiltinPlayerThread.setName("Kyokuerabu-BuiltinPlayer");
        currentBuiltinPlayerThread.start();

        // 更新HUD显示（与外部播放器相同的逻辑）
        String displayName = fileName.replaceAll("\\.[^.]*$", "");
        System.out.println("[Kyokuerabu] Built-in player: Updating HUD for " + displayName);
        System.out.println("[Kyokuerabu] Show HUD enabled: " + MusicConfig.showHud());

        if (MusicConfig.showHud()) {
            System.out.println("[Kyokuerabu] Built-in player: Updating HUD");
            MusicHUD.setCurrentMusic(displayName, getCurrentPlayerName());
        } else {
            System.out.println("[Kyokuerabu] Built-in player: HUD disabled, skipping HUD update");
        }

        sendMessage("§a[Kyokuerabu] ♪ 使用内置播放器播放: " + displayName);
    }

    public static void stopCurrentMusic() {
        try {
            // 停止外部播放器进程
            if (currentPlayerProcess != null && currentPlayerProcess.isAlive()) {
                currentPlayerProcess.destroyForcibly();
                LOGGER.info("Stopped external player process");
            }

            // 停止内置播放器
            stopBuiltinPlayer();

            // 清除HUD显示
            MusicHUD.clearCurrentMusic();

        } catch (Exception e) {
            LOGGER.error("Error stopping current music: " + e.getMessage(), e);
        }
    }

    private static void stopBuiltinPlayer() {
        try {
            LOGGER.info("Stopping built-in player...");
            shouldStopBuiltin = true;

            // 停止播放器
            if (currentBuiltinPlayer != null) {
                try {
                    // 使用反射调用stop方法
                    Class<?> playerClass = currentBuiltinPlayer.getClass();
                    playerClass.getMethod("stop").invoke(currentBuiltinPlayer);
                    LOGGER.info("Stopped built-in player via stop() method");
                } catch (Exception e) {
                    LOGGER.warn("Failed to stop built-in player gracefully: {}", e.getMessage());
                    // 尝试调用close方法
                    try {
                        Class<?> playerClass = currentBuiltinPlayer.getClass();
                        playerClass.getMethod("close").invoke(currentBuiltinPlayer);
                        LOGGER.info("Stopped built-in player via close() method");
                    } catch (Exception e2) {
                        LOGGER.warn("Failed to close built-in player: {}", e2.getMessage());
                    }
                }
                currentBuiltinPlayer = null;
            }

            // 停止线程
            if (currentBuiltinPlayerThread != null && currentBuiltinPlayerThread.isAlive()) {
                try {
                    LOGGER.info("Interrupting built-in player thread...");
                    currentBuiltinPlayerThread.interrupt();

                    // 等待线程结束
                    currentBuiltinPlayerThread.join(2000); // 等待最多2秒
                    if (currentBuiltinPlayerThread.isAlive()) {
                        LOGGER.warn("Built-in player thread did not stop within timeout");
                        // 不使用已过时的stop()方法，而是让线程自然结束
                    }
                    LOGGER.info("Built-in player thread stopped");
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    LOGGER.warn("Interrupted while stopping built-in player thread");
                } catch (Exception e) {
                    LOGGER.error("Error stopping built-in player thread: {}", e.getMessage());
                }
                currentBuiltinPlayerThread = null;
            }

            LOGGER.info("Built-in player stop completed");
        } catch (Exception e) {
            LOGGER.error("Error stopping built-in player: {}", e.getMessage());
        }
    }

    private static String getCurrentPlayerName() {
        try {
            Minecraft mc = Minecraft.getInstance();
            if (mc.player != null) {
                return mc.player.getName().getString();
            }
        } catch (Exception e) {
            LOGGER.warn("Failed to get player name: " + e.getMessage());
        }
        return "Unknown";
    }

    private static void sendMessage(String message) {
        try {
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(Component.literal(message));
                }
            });
        } catch (Exception e) {
            LOGGER.error("Failed to send message: " + e.getMessage());
        }
    }

    // 检查播放器进程是否还在运行
    public static boolean isPlayerRunning() {
        return (currentPlayerProcess != null && currentPlayerProcess.isAlive()) ||
               (currentBuiltinPlayerThread != null && currentBuiltinPlayerThread.isAlive());
    }

    // 设置音量（仅对内置播放器有效）
    public static void setVolume(double volume) {
        // 外部播放器的音量在启动时设置，无法动态调整
        // 内置播放器目前也无法动态调整音量，但可以在这里添加系统音量控制
        try {
            adjustSystemVolume(volume);
        } catch (Exception e) {
            LOGGER.warn("Failed to adjust system volume: {}", e.getMessage());
        }
    }

    private static void adjustSystemVolume(double volume) {
        try {
            // 尝试调节系统音量
            javax.sound.sampled.Mixer.Info[] mixers = javax.sound.sampled.AudioSystem.getMixerInfo();
            for (javax.sound.sampled.Mixer.Info mixerInfo : mixers) {
                javax.sound.sampled.Mixer mixer = javax.sound.sampled.AudioSystem.getMixer(mixerInfo);
                if (mixer.isLineSupported(javax.sound.sampled.Port.Info.SPEAKER)) {
                    javax.sound.sampled.Port port = (javax.sound.sampled.Port) mixer.getLine(javax.sound.sampled.Port.Info.SPEAKER);
                    port.open();

                    if (port.isControlSupported(javax.sound.sampled.FloatControl.Type.VOLUME)) {
                        javax.sound.sampled.FloatControl volumeControl = (javax.sound.sampled.FloatControl) port.getControl(javax.sound.sampled.FloatControl.Type.VOLUME);
                        float newVolume = (float) volume;
                        volumeControl.setValue(Math.max(volumeControl.getMinimum(),
                                             Math.min(volumeControl.getMaximum(), newVolume)));
                    }
                    port.close();
                    break;
                }
            }
        } catch (Exception e) {
            // 音量调节失败，忽略
        }
    }

    // 游戏退出时清理
    public static void onGameExit() {
        stopCurrentMusic();
    }
}
