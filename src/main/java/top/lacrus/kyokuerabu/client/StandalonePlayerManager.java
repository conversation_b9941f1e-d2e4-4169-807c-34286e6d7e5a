package top.lacrus.kyokuerabu.client;

import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.nio.file.*;
import java.lang.management.ManagementFactory;

/**
 * 管理独立音频播放器JAR文件的类
 * 负责在模组启动时创建目录并复制JAR文件
 */
@OnlyIn(Dist.CLIENT)
public class StandalonePlayerManager {
    private static final Logger LOGGER = LogManager.getLogger();
    private static final String PLAYER_DIR_NAME = "kyokuerabu_player";
    private static final String PLAYER_JAR_NAME = "standalone-audio-player.jar";
    
    private static Path playerDirectory;
    private static Path playerJarPath;
    private static boolean initialized = false;
    
    /**
     * 初始化独立播放器，创建目录并复制JAR文件
     */
    public static void initialize() {
        if (initialized) {
            return;
        }
        
        try {
            // 创建播放器目录
            playerDirectory = Paths.get(PLAYER_DIR_NAME);
            if (!Files.exists(playerDirectory)) {
                Files.createDirectories(playerDirectory);
                LOGGER.info("Created player directory: {}", playerDirectory.toAbsolutePath());
            }
            
            // 设置JAR文件路径
            playerJarPath = playerDirectory.resolve(PLAYER_JAR_NAME);
            
            // 复制JAR文件
            copyPlayerJar();
            
            initialized = true;
            LOGGER.info("StandalonePlayerManager initialized successfully");
            
        } catch (Exception e) {
            LOGGER.error("Failed to initialize StandalonePlayerManager: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 复制独立播放器JAR文件到目标目录
     */
    private static void copyPlayerJar() throws IOException {
        // 尝试从构建输出目录复制JAR文件
        Path buildJarPath = Paths.get("build", "libs", "standalone-audio-player-" + getModVersion() + ".jar");
        
        if (Files.exists(buildJarPath)) {
            // 从构建目录复制
            Files.copy(buildJarPath, playerJarPath, StandardCopyOption.REPLACE_EXISTING);
            LOGGER.info("Copied player JAR from build directory: {}", buildJarPath);
        } else {
            // 尝试从资源中提取（如果JAR被打包到模组中）
            try (InputStream jarStream = StandalonePlayerManager.class.getResourceAsStream("/standalone-audio-player.jar")) {
                if (jarStream != null) {
                    Files.copy(jarStream, playerJarPath, StandardCopyOption.REPLACE_EXISTING);
                    LOGGER.info("Extracted player JAR from resources");
                } else {
                    // 如果都找不到，创建一个临时的JAR文件
                    createTemporaryPlayerJar();
                }
            }
        }
    }
    
    /**
     * 创建临时的播放器JAR文件（包含必要的类）
     */
    private static void createTemporaryPlayerJar() throws IOException {
        LOGGER.warn("Could not find pre-built player JAR, creating temporary JAR");
        
        // 这里可以实现从当前classpath提取必要类的逻辑
        // 暂时创建一个空的JAR文件作为占位符
        try (FileOutputStream fos = new FileOutputStream(playerJarPath.toFile());
             java.util.jar.JarOutputStream jos = new java.util.jar.JarOutputStream(fos)) {
            
            // 添加manifest
            java.util.jar.Manifest manifest = new java.util.jar.Manifest();
            manifest.getMainAttributes().put(java.util.jar.Attributes.Name.MANIFEST_VERSION, "1.0");
            manifest.getMainAttributes().put(java.util.jar.Attributes.Name.MAIN_CLASS, 
                "top.lacrus.kyokuerabu.standalone.StandaloneAudioPlayer");
            
            // 这里需要添加实际的类文件
            // 由于复杂性，建议在构建时确保JAR文件存在
        }
        
        LOGGER.info("Created temporary player JAR: {}", playerJarPath);
    }
    
    /**
     * 获取模组版本
     */
    private static String getModVersion() {
        // 尝试从系统属性或其他方式获取版本
        String version = StandalonePlayerManager.class.getPackage().getImplementationVersion();
        return version != null ? version : "1.0-SNAPSHOT"; // 默认版本
    }
    
    /**
     * 获取播放器JAR文件路径
     */
    public static Path getPlayerJarPath() {
        if (!initialized) {
            initialize();
        }
        return playerJarPath;
    }
    
    /**
     * 获取当前Minecraft使用的Java可执行文件路径
     */
    public static String getMinecraftJavaPath() {
        try {
            // 获取当前JVM的路径
            String javaHome = System.getProperty("java.home");
            String osName = System.getProperty("os.name").toLowerCase();
            
            Path javaPath;
            if (osName.contains("win")) {
                // Windows
                javaPath = Paths.get(javaHome, "bin", "java.exe");
                if (!Files.exists(javaPath)) {
                    javaPath = Paths.get(javaHome, "bin", "javaw.exe");
                }
            } else {
                // Linux/Mac
                javaPath = Paths.get(javaHome, "bin", "java");
            }
            
            if (Files.exists(javaPath)) {
                LOGGER.debug("Found Java executable: {}", javaPath);
                return javaPath.toAbsolutePath().toString();
            }
            
        } catch (Exception e) {
            LOGGER.warn("Failed to get Minecraft Java path: {}", e.getMessage());
        }
        
        // 回退到系统默认的java命令
        return "java";
    }
    
    /**
     * 启动独立音频播放器
     */
    public static Process startPlayer(Path musicFile, String fileName) throws IOException {
        if (!initialized) {
            initialize();
        }
        
        String javaPath = getMinecraftJavaPath();
        String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];
        String volume = String.valueOf(MusicConfig.getVolume());
        
        ProcessBuilder pb = new ProcessBuilder(
            javaPath,
            "-jar", playerJarPath.toAbsolutePath().toString(),
            musicFile.toAbsolutePath().toString(),
            currentPid,
            volume
        );
        
        pb.redirectErrorStream(true);
        pb.redirectOutput(ProcessBuilder.Redirect.DISCARD);
        
        LOGGER.info("Starting player: {} -jar {} {} {} {}", 
            javaPath, playerJarPath, musicFile, currentPid, volume);
        
        return pb.start();
    }
    
    /**
     * 检查播放器是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 清理资源
     */
    public static void cleanup() {
        // 可以在这里添加清理逻辑
        LOGGER.info("StandalonePlayerManager cleanup completed");
    }
}
