package top.lacrus.kyokuerabu.client;

import net.minecraft.client.Minecraft;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.InputEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = "kyokuerabu", value = Dist.CLIENT)
public class KeyInputHandler {

    @SubscribeEvent
    public static void onKeyInput(InputEvent.Key event) {
        Minecraft mc = Minecraft.getInstance();
        
        if (mc.screen != null) {
            return; // 如果有GUI打开，不处理按键
        }
        
        if (KeyBindings.OPEN_MUSIC_CONFIG.consumeClick()) {
            mc.setScreen(new MusicConfigScreen(null));
        }
        
        if (KeyBindings.TOGGLE_HUD.consumeClick()) {
            boolean currentShow = MusicConfig.showHud();
            MusicConfig.setShowHud(!currentShow);
            
            // 安全保存配置
            try {
                MusicConfig.SPEC.save();
            } catch (Exception e) {
                // 配置保存失败时忽略
            }
            
            if (!currentShow && MusicHUD.isPlaying()) {
                MusicHUD.showHUD();
            } else {
                MusicHUD.hideHUD();
            }
            
            if (mc.player != null) {
                String message = currentShow ? "§e[Kyokuerabu] HUD已隐藏" : "§a[Kyokuerabu] HUD已显示";
                mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal(message));
            }
        }
        
        if (KeyBindings.STOP_MUSIC.consumeClick()) {
            System.out.println("[Kyokuerabu] Stop music key pressed!");

            // 检查是否有音乐在播放
            boolean wasPlaying = MinecraftJLayerPlayer.isPlayerRunning();
            System.out.println("[Kyokuerabu] Was playing: " + wasPlaying);

            // 停止所有音乐播放器
            ClientEventHandler.stopAllMusic();

            // 显示停止提示
            if (mc.player != null) {
                String message = wasPlaying ? "§c[Kyokuerabu] ♪ 音乐已停止" : "§e[Kyokuerabu] 没有正在播放的音乐";
                mc.player.sendSystemMessage(
                    net.minecraft.network.chat.Component.literal(message)
                );
            }

            System.out.println("[Kyokuerabu] Stop music key processing completed");
        }
    }
}
