package top.lacrus.kyokuerabu.client;

import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.ClientPlayerNetworkEvent;
import net.minecraftforge.event.TickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.event.server.ServerStoppingEvent;

import java.util.concurrent.ConcurrentLinkedQueue;

@OnlyIn(Dist.CLIENT)
@Mod.EventBusSubscriber(modid = "kyokuerabu", value = Dist.CLIENT)
public class ClientEventHandler {
    
    // 用于存储待播放的音乐URL队列
    private static final ConcurrentLinkedQueue<String> musicQueue = new ConcurrentLinkedQueue<>();
    
    public static void queueMusic(String musicUrl) {
        System.out.println("[<PERSON><PERSON><PERSON><PERSON><PERSON>] Queueing music for playback: " + musicUrl);
        musicQueue.offer(musicUrl);
    }
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase == TickEvent.Phase.END) {
            // 检查是否有待播放的音乐
            String musicUrl = musicQueue.poll();
            if (musicUrl != null) {
                System.out.println("[Kyokuerabu] Processing queued music: " + musicUrl);
                
                // 向玩家发送消息
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§a[Kyokuerabu] 开始播放音乐: " + musicUrl)
                    );
                }
                
                // 播放音乐
                ClientMusicPlayer.playMusic(musicUrl);
            }
        }
    }

    @SubscribeEvent
    public static void onPlayerLoggedOut(ClientPlayerNetworkEvent.LoggingOut event) {
        // 玩家退出服务器时停止音乐播放
        MinecraftJLayerPlayer.onGameExit();
    }
}
