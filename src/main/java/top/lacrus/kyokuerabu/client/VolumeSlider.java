package top.lacrus.kyokuerabu.client;

import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.AbstractSliderButton;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

@OnlyIn(Dist.CLIENT)
public class VolumeSlider extends AbstractSliderButton {
    private final Component prefix;
    
    public VolumeSlider(int x, int y, int width, int height, Component prefix, double value) {
        super(x, y, width, height, Component.empty(), value);
        this.prefix = prefix;
        this.updateMessage();
    }
    
    @Override
    protected void updateMessage() {
        int percentage = (int) (this.value * 100);
        this.setMessage(Component.literal(this.prefix.getString() + percentage + "%"));
    }
    
    @Override
    protected void applyValue() {
        // 实时应用音量变化
        MusicConfig.setVolume(this.value);

        // 如果当前有音乐在播放，调节音量
        if (MinecraftJLayerPlayer.isPlayerRunning()) {
            MinecraftJLayerPlayer.setVolume(this.value);

            // 显示音量变化提示
            try {
                net.minecraft.client.Minecraft mc = net.minecraft.client.Minecraft.getInstance();
                if (mc.player != null) {
                    int percentage = (int) (this.value * 100);
                    mc.player.sendSystemMessage(
                        net.minecraft.network.chat.Component.literal("§e[Kyokuerabu] 音量已调整为: " + percentage + "%")
                    );
                }
            } catch (Exception e) {
                // 忽略消息发送失败
            }
        }
    }


    
    public double getValue() {
        return this.value;
    }
    
    public void setValue(double value) {
        this.value = Math.max(0.0, Math.min(1.0, value));
        this.updateMessage();
    }
}
