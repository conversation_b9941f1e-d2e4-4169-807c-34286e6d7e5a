package top.lacrus.kyokuerabu.client;

import com.mojang.blaze3d.vertex.PoseStack;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@OnlyIn(Dist.CLIENT)
@Mod.EventBusSubscriber(modid = "kyokuerabu", value = Dist.CLIENT)
public class MusicHUD {
    private static String currentMusicName = null;
    private static String currentPlayerName = null;
    private static long musicStartTime = 0;
    private static boolean isVisible = false;

    public static void setCurrentMusic(String musicName, String playerName) {
        System.out.println("[Kyokuerabu] MusicHUD.setCurrentMusic called:");
        System.out.println("  Music: " + musicName);
        System.out.println("  Player: " + playerName);

        currentMusicName = musicName;
        currentPlayerName = playerName;
        musicStartTime = System.currentTimeMillis();
        isVisible = true;

        System.out.println("[Kyokuerabu] MusicHUD updated successfully");
    }

    public static void clearCurrentMusic() {
        currentMusicName = null;
        currentPlayerName = null;
        musicStartTime = 0;
        isVisible = false;
    }

    @SubscribeEvent
    public static void onRenderGuiOverlay(RenderGuiOverlayEvent.Post event) {
        if (!isVisible || currentMusicName == null || !MusicConfig.showHud()) {
            return;
        }

        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.options.hideGui) {
            return;
        }

        GuiGraphics guiGraphics = event.getGuiGraphics();
        Font font = mc.font;
        
        int screenWidth = mc.getWindow().getGuiScaledWidth();
        int screenHeight = mc.getWindow().getGuiScaledHeight();

        // HUD位置：右上角
        int hudX = screenWidth - 10;
        int hudY = 10;

        // 计算播放时间
        long playTime = (System.currentTimeMillis() - musicStartTime) / 1000;
        String timeStr = formatTime(playTime);

        // 准备显示文本
        String musicText = "♪ " + currentMusicName;
        String playerText = "播放者: " + currentPlayerName;
        String timeText = "已播放: " + timeStr;

        // 计算文本宽度
        int musicWidth = font.width(musicText);
        int playerWidth = font.width(playerText);
        int timeWidth = font.width(timeText);
        int maxWidth = Math.max(Math.max(musicWidth, playerWidth), timeWidth);

        // HUD背景
        int bgX = hudX - maxWidth - 15;
        int bgY = hudY - 5;
        int bgWidth = maxWidth + 20;
        int bgHeight = 45; // 减少高度，移除进度条空间

        // 绘制半透明背景
        guiGraphics.fill(bgX, bgY, bgX + bgWidth, bgY + bgHeight, 0x80000000);

        // 绘制边框
        guiGraphics.fill(bgX, bgY, bgX + bgWidth, bgY + 1, 0xFF555555); // 上边框
        guiGraphics.fill(bgX, bgY + bgHeight - 1, bgX + bgWidth, bgY + bgHeight, 0xFF555555); // 下边框
        guiGraphics.fill(bgX, bgY, bgX + 1, bgY + bgHeight, 0xFF555555); // 左边框
        guiGraphics.fill(bgX + bgWidth - 1, bgY, bgX + bgWidth, bgY + bgHeight, 0xFF555555); // 右边框

        // 绘制文本
        int textX = hudX - maxWidth - 5;
        int textY = hudY;

        // 音乐名称 (绿色)
        guiGraphics.drawString(font, musicText, textX, textY, 0x55FF55);
        textY += 12;

        // 播放者 (黄色)
        guiGraphics.drawString(font, playerText, textX, textY, 0xFFFF55);
        textY += 12;

        // 播放时间 (白色)
        guiGraphics.drawString(font, timeText, textX, textY, 0xFFFFFF);
    }

    private static String formatTime(long seconds) {
        long minutes = seconds / 60;
        long remainingSeconds = seconds % 60;
        return String.format("%02d:%02d", minutes, remainingSeconds);
    }



    // 检查播放器进程是否还在运行
    public static void checkPlayerStatus() {
        if (isVisible && currentMusicName != null) {
            // 检查外部播放器进程是否还在运行
            if (!MinecraftJLayerPlayer.isPlayerRunning()) {
                // 进程结束，自动清除HUD
                clearCurrentMusic();
            }
        }
    }

    // 手动隐藏HUD
    public static void hideHUD() {
        isVisible = false;
    }

    // 手动显示HUD
    public static void showHUD() {
        if (currentMusicName != null) {
            isVisible = true;
        }
    }

    // 获取当前播放状态
    public static boolean isPlaying() {
        return isVisible && currentMusicName != null;
    }

    // 获取当前音乐名称
    public static String getCurrentMusicName() {
        return currentMusicName;
    }

    // 获取当前播放者
    public static String getCurrentPlayerName() {
        return currentPlayerName;
    }
}
