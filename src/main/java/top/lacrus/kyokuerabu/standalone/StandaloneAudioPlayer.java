package top.lacrus.kyokuerabu.standalone;

import javazoom.jl.player.advanced.AdvancedPlayer;
import javazoom.jl.player.advanced.PlaybackEvent;
import javazoom.jl.player.advanced.PlaybackListener;
import javax.sound.sampled.*;
import java.util.Arrays;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 独立的音频播放器，可以在单独的Java进程中运行
 * 不依赖Minecraft环境，专门用于播放音频文件
 * 包含Minecraft进程检测功能，当MC进程退出时自动退出
 */
public class StandaloneAudioPlayer {
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private static volatile boolean shouldExit = false;
    private static String minecraftProcessId;
    private static double volume = 1.0;
    
    public static void main(String[] args) {
        if (args.length < 1) {
            System.err.println("Usage: StandaloneAudioPlayer <file> [pid] [volume]");
            System.exit(1);
        }

        String filePath = args[0];
        Path audioFile = Paths.get(filePath);

        if (!Files.exists(audioFile)) {
            System.err.println("Audio file does not exist: " + filePath);
            System.exit(1);
        }

        // 检查jlayer依赖是否可用
        try {
            Class.forName("javazoom.jl.player.advanced.AdvancedPlayer");
        } catch (ClassNotFoundException e) {
            System.err.println("JLayer library not found in classpath. Cannot play MP3 files.");
            System.err.println("Classpath: " + System.getProperty("java.class.path"));
            System.exit(1);
        }

        // 如果提供了Minecraft进程ID，使用它；否则尝试获取父进程ID
        if (args.length >= 2) {
            minecraftProcessId = args[1];
        } else {
            minecraftProcessId = getParentProcessId();
        }

        // 如果提供了音量参数，使用它
        if (args.length >= 3) {
            try {
                volume = Double.parseDouble(args[2]);
                volume = Math.max(0.0, Math.min(1.0, volume));
            } catch (NumberFormatException e) {
                volume = 1.0;
            }
        }

        try {
            // 启动Minecraft进程监控
            startMinecraftProcessMonitor();

            // 添加关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                cleanup();
            }));

            playAudioFile(audioFile);

        } catch (Exception e) {
            System.exit(1);
        }
    }
    
    private static String getParentProcessId() {
        try {
            // 获取当前进程的PID
            String currentPid = ManagementFactory.getRuntimeMXBean().getName().split("@")[0];

            // 在Windows上使用wmic获取父进程ID
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                ProcessBuilder pb = new ProcessBuilder("wmic", "process", "where", "processid=" + currentPid, "get", "parentprocessid", "/value");
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.startsWith("ParentProcessId=")) {
                        return line.substring("ParentProcessId=".length()).trim();
                    }
                }
            } else {
                // 在Linux/Mac上使用ps获取父进程ID
                ProcessBuilder pb = new ProcessBuilder("ps", "-o", "ppid=", "-p", currentPid);
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String ppid = reader.readLine();
                if (ppid != null) {
                    return ppid.trim();
                }
            }
        } catch (Exception e) {
            // 如果获取失败，返回null
        }
        return null;
    }

    private static void startMinecraftProcessMonitor() {
        if (minecraftProcessId == null) {
            return;
        }

        // 每2秒检查一次Minecraft进程是否还存在
        scheduler.scheduleAtFixedRate(() -> {
            if (shouldExit) {
                return;
            }

            if (!isProcessAlive(minecraftProcessId)) {
                // Minecraft进程已退出，立即退出音乐播放器
                shouldExit = true;
                cleanup();
                System.exit(0);
            }
        }, 2, 2, TimeUnit.SECONDS);
    }

    private static boolean isProcessAlive(String pid) {
        try {
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                // Windows: 使用tasklist检查进程
                ProcessBuilder pb = new ProcessBuilder("tasklist", "/fi", "PID eq " + pid, "/fo", "csv");
                Process process = pb.start();
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line;
                int lineCount = 0;
                while ((line = reader.readLine()) != null) {
                    lineCount++;
                    if (lineCount > 1) { // 跳过标题行
                        return true; // 找到进程
                    }
                }
                return false;
            } else {
                // Linux/Mac: 使用ps检查进程
                ProcessBuilder pb = new ProcessBuilder("ps", "-p", pid);
                Process process = pb.start();
                return process.waitFor() == 0;
            }
        } catch (Exception e) {
            // 如果检查失败，假设进程已退出
            return false;
        }
    }

    private static void playAudioFile(Path audioFile) {
        try {
            // 如果音量为0，直接退出
            if (volume <= 0.0) {
                System.exit(0);
                return;
            }

            // 尝试使用系统音量控制
            if (trySystemVolumeControl()) {
                playWithAdvancedPlayer(audioFile);
            } else {
                playWithAdvancedPlayer(audioFile);
            }

        } catch (Exception e) {
            System.exit(1);
        }
    }

    private static boolean trySystemVolumeControl() {
        try {
            // 获取系统音频混合器
            Mixer.Info[] mixers = AudioSystem.getMixerInfo();
            for (Mixer.Info mixerInfo : mixers) {
                Mixer mixer = AudioSystem.getMixer(mixerInfo);
                if (mixer.isLineSupported(Port.Info.SPEAKER)) {
                    Port port = (Port) mixer.getLine(Port.Info.SPEAKER);
                    port.open();

                    if (port.isControlSupported(FloatControl.Type.VOLUME)) {
                        FloatControl volumeControl = (FloatControl) port.getControl(FloatControl.Type.VOLUME);
                        float currentVolume = volumeControl.getValue();
                        float newVolume = (float) (currentVolume * volume);
                        volumeControl.setValue(Math.max(volumeControl.getMinimum(),
                                             Math.min(volumeControl.getMaximum(), newVolume)));
                        port.close();
                        return true;
                    }
                    port.close();
                }
            }
        } catch (Exception e) {
            // 系统音量控制失败，使用软件音量控制
        }
        return false;
    }

    private static void playWithAdvancedPlayer(Path audioFile) {
        try {
            // 创建输入流
            FileInputStream fis = new FileInputStream(audioFile.toFile());
            BufferedInputStream bis = new BufferedInputStream(fis);

            // 创建AdvancedPlayer
            AdvancedPlayer player = new AdvancedPlayer(bis);

            // 添加播放监听器
            player.setPlayBackListener(new PlaybackListener() {
                @Override
                public void playbackStarted(PlaybackEvent evt) {
                    // 静默启动
                }

                @Override
                public void playbackFinished(PlaybackEvent evt) {
                    cleanup();
                    System.exit(0);
                }
            });

            // 在播放过程中定期检查是否应该退出
            Thread playThread = new Thread(() -> {
                try {
                    player.play();
                } catch (Exception e) {
                    // 播放出错
                }
            });

            playThread.start();

            // 主线程等待播放完成或收到退出信号
            while (playThread.isAlive() && !shouldExit) {
                Thread.sleep(100);
            }

            if (shouldExit) {
                player.close();
            }

        } catch (Exception e) {
            System.exit(1);
        }
    }

    private static void cleanup() {
        shouldExit = true;
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
    
    /**
     * 简单的播放方法，用于测试
     */
    public static void simplePlay(String filePath) {
        try {
            Path audioFile = Paths.get(filePath);
            
            if (!Files.exists(audioFile)) {
                System.out.println("文件不存在: " + audioFile.toAbsolutePath());
                return;
            }
            
            System.out.println("简单播放: " + audioFile.getFileName());
            
            FileInputStream fis = new FileInputStream(audioFile.toFile());
            AdvancedPlayer player = new AdvancedPlayer(fis);
            
            // 阻塞播放
            player.play();
            
            System.out.println("简单播放完成");
            
        } catch (Exception e) {
            System.out.println("简单播放失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 后台播放方法
     */
    public static void backgroundPlay(String filePath) {
        Thread playThread = new Thread(() -> {
            simplePlay(filePath);
        });
        
        playThread.setDaemon(false); // 确保进程不会立即退出
        playThread.setName("StandaloneAudioPlayer");
        playThread.start();
        
        try {
            playThread.join(); // 等待播放完成
        } catch (InterruptedException e) {
            System.out.println("播放被中断");
        }
    }
    
    /**
     * 循环播放方法
     */
    public static void loopPlay(String filePath, int times) {
        for (int i = 0; i < times; i++) {
            System.out.println("播放第 " + (i + 1) + " 次");
            simplePlay(filePath);
            
            if (i < times - 1) {
                try {
                    Thread.sleep(1000); // 播放间隔1秒
                } catch (InterruptedException e) {
                    break;
                }
            }
        }
    }
}
